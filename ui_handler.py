import cv2
import numpy as np
import time
import functools # For passing app_state to mouse callback
import logging
import subprocess # <--- 添加 subprocess
import os         # <--- 添加 os
import sys        # <--- 添加 sys 用于获取Python解释器路径
import re         # <--- 添加 re 用于解析分析结果
import traceback  # <--- 添加 traceback 用于详细错误信息

from app_state import AppState
from constants import *
import camera_manager # Need to call apply_camera_settings
import config_manager # Need to call save_config
import led_detector   # Need to call LED detection/calibration functions
import digit_detector # Need to call digit detection functions
from cpu_communicator import send_value_to_cpu # <--- 添加 CPU 通信函数
import base_point_manager # <--- 添加基准点管理模块

# 尝试导入分析函数，添加错误处理
try:
    from analyze_led_log import analyze_led_cycles
    ANALYSIS_IMPORT_SUCCESS = True
    print("Successfully imported analyze_led_cycles function")
except ImportError as e:
    ANALYSIS_IMPORT_SUCCESS = False
    print(f"Failed to import analyze_led_cycles: {e}")
    analyze_led_cycles = None

# --- Mouse Callback ---
def _mouse_callback(event, x, y, flags, param, app_state: AppState):
    """Internal mouse callback function. Uses functools.partial to get app_state."""
    # 只在校准模式下的特定状态允许绘制
    allow_drawing = (app_state.current_mode == MODE_CALIBRATION and
                     app_state.current_calib_state in [CALIB_STATE_BASE_POINTS_SELECT,
                                                       CALIB_STATE_LED_ROI_SELECT,
                                                       CALIB_STATE_LED_EDIT,
                                                       CALIB_STATE_DIGIT_ROI_SELECT_1,
                                                       CALIB_STATE_DIGIT_ROI_SELECT_2,
                                                       CALIB_STATE_DIGIT_SEGMENT_SELECT])

    if not allow_drawing:
        app_state.selecting_roi = False
        app_state.current_rect = None
        app_state.template_preview_pos = None  # 清空模板预览
        return

    # --- 基准点选择处理 (仅在基准点选择状态下) ---
    if app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
        if event == cv2.EVENT_LBUTTONDOWN:
            idx = app_state.calib_base_point_index
            if idx < 2:
                try:
                    # 提取基准点模板
                    template = base_point_manager.extract_base_template(
                        app_state.current_frame, x, y, app_state.base_template_size
                    )
                    if template is not None:
                        app_state.base_points[idx] = (x, y)
                        app_state.base_templates[idx] = template
                        print(f"✓ 基准点 {idx + 1} 已选择: ({x}, {y}), 模板大小: {template.shape}")
                        app_state.calib_base_point_index += 1

                        # 给出下一步提示
                        if idx == 0:
                            print("请选择第二个基准点（建议选择与第一个点有一定距离的特征点）")
                        else:
                            print("两个基准点已选择完成，按Enter继续或按R重新选择")
                    else:
                        print("❌ 基准点选择失败！请选择对比度更高的特征点（如螺丝孔、标记、清晰边角）")
                        print("提示：避免选择平坦区域或光照不均匀的位置")

                except Exception as e:
                    print(f"❌ 基准点选择时发生错误: {e}")
                    logging.error(f"基准点选择异常: {e}")
        return  # 基准点选择模式下不执行其他逻辑

    # --- 模板模式处理 (仅在 LED ROI 选择状态下) ---
    if (app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT and
        app_state.template_mode and app_state.template_roi):

        if event == cv2.EVENT_MOUSEMOVE:
            # 更新模板预览位置 (以鼠标为中心)
            w, h = app_state.template_roi
            app_state.template_preview_pos = (x - w//2, y - h//2, w, h)

        elif event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否超出 LED ROI 数量限制
            if app_state.calib_led_roi_index >= app_state.led_max_rois:
                print(f"已达到最大 LED ROI 数量 ({app_state.led_max_rois})。")
                return
            # 在点击位置放置模板ROI
            w, h = app_state.template_roi
            app_state.current_rect = (x - w//2, y - h//2, w, h)
            print(f"模板ROI已放置在位置: ({x - w//2}, {y - h//2})")
        return  # 模板模式下不执行常规的拖拽逻辑

    # --- ROI编辑模式处理 (仅在 LED ROI 编辑状态下) ---
    if app_state.current_calib_state == CALIB_STATE_LED_EDIT:

        if event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否点击在某个ROI上
            clicked_roi_index = -1
            for i, roi in enumerate(app_state.led_rois):
                if roi and isinstance(roi, tuple) and len(roi) == 4:
                    rx, ry, rw, rh = roi
                    if rx <= x <= rx + rw and ry <= y <= ry + rh:
                        clicked_roi_index = i
                        break

            if clicked_roi_index != -1:
                # 点击在ROI上，开始移动
                app_state.selected_roi_index = clicked_roi_index
                app_state.moving_roi = True
                app_state.move_start_pos = (x, y)
                app_state.original_roi_pos = app_state.led_rois[clicked_roi_index]
                is_green = clicked_roi_index < app_state.led_num_green
                led_label = f"G{clicked_roi_index+1}" if is_green else f"R{clicked_roi_index - app_state.led_num_green + 1}"
                print(f"开始移动 {led_label} ROI")
            else:
                # 点击在空白处，取消选择
                app_state.selected_roi_index = -1
                app_state.moving_roi = False

        elif event == cv2.EVENT_MOUSEMOVE:
            if app_state.moving_roi and app_state.selected_roi_index != -1:
                # 计算移动偏移量
                if app_state.move_start_pos and app_state.original_roi_pos:
                    start_x, start_y = app_state.move_start_pos
                    offset_x = x - start_x
                    offset_y = y - start_y

                    # 更新ROI位置
                    orig_x, orig_y, orig_w, orig_h = app_state.original_roi_pos
                    new_x = orig_x + offset_x
                    new_y = orig_y + offset_y
                    app_state.led_rois[app_state.selected_roi_index] = (new_x, new_y, orig_w, orig_h)

        elif event == cv2.EVENT_LBUTTONUP:
            if app_state.moving_roi:
                app_state.moving_roi = False
                if app_state.selected_roi_index != -1:
                    is_green = app_state.selected_roi_index < app_state.led_num_green
                    led_label = f"G{app_state.selected_roi_index+1}" if is_green else f"R{app_state.selected_roi_index - app_state.led_num_green + 1}"
                    new_pos = app_state.led_rois[app_state.selected_roi_index]
                    print(f"{led_label} ROI 移动完成: {new_pos}")
        return  # 编辑模式下不执行常规的拖拽逻辑

    # --- 常规拖拽模式处理 ---
    if event == cv2.EVENT_LBUTTONDOWN:
        # 检查是否超出 LED ROI 数量限制
        if app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT and \
           app_state.calib_led_roi_index >= app_state.led_max_rois:
             print(f"已达到最大 LED ROI 数量 ({app_state.led_max_rois})。")
             return
        app_state.selecting_roi = True
        app_state.roi_start_point = (x, y)
        app_state.current_rect = None

    elif event == cv2.EVENT_MOUSEMOVE:
        if app_state.selecting_roi:
            x1, y1 = app_state.roi_start_point
            x2, y2 = x, y
            start_x, start_y = min(x1, x2), min(y1, y2)
            end_x, end_y = max(x1, x2), max(y1, y2)
            w, h = end_x - start_x, end_y - start_y
            # 只有当 ROI 有效时才更新 current_rect
            if w > 0 and h > 0:
                app_state.current_rect = (start_x, start_y, w, h)
            else:
                app_state.current_rect = None # 防止无效 ROI

    elif event == cv2.EVENT_LBUTTONUP:
        if app_state.selecting_roi:
            app_state.selecting_roi = False
            # 检查 current_rect 是否有效，防止快速点击产生 None 或无效 ROI
            if app_state.current_rect is None or app_state.current_rect[2] < ROI_MIN_SIZE or app_state.current_rect[3] < ROI_MIN_SIZE:
                print("ROI 选择无效 (尺寸过小或未移动)。")
                app_state.current_rect = None # 重置无效的 ROI
            # else: current_rect 保留，等待按键确认


def setup_mouse_callback(window_name: str, app_state: AppState):
    """Sets up the mouse callback for the main window."""
    # 使用 functools.partial 将 app_state 实例传递给回调函数
    callback_with_state = functools.partial(_mouse_callback, app_state=app_state)
    cv2.setMouseCallback(window_name, callback_with_state)

# --- Drawing Functions ---

def draw_rois(app_state: AppState):
    """在 display_frame 上绘制所有相关的 ROI"""
    if app_state.display_frame is None: return

    # --- 在校准模式下绘制 ---
    if app_state.current_mode == MODE_CALIBRATION:
        # 基准点选择
        if app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
            # 绘制已选择的基准点
            for i, point in enumerate(app_state.base_points):
                if point:
                    x, y = point
                    # 绘制基准点标记
                    cv2.circle(app_state.display_frame, (x, y), 8, (0, 255, 255), 2)  # 黄色圆圈
                    cv2.circle(app_state.display_frame, (x, y), 2, (0, 0, 255), -1)   # 红色中心点
                    # 绘制基准点编号
                    cv2.putText(app_state.display_frame, f"P{i+1}",
                               (x + 12, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    # 绘制模板区域边框
                    half_size = app_state.base_template_size // 2
                    cv2.rectangle(app_state.display_frame,
                                 (x - half_size, y - half_size),
                                 (x + half_size, y + half_size),
                                 (255, 255, 0), 1)  # 青色模板边框
        # LED ROI 选择
        if app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT:
            # 绘制已确认的 ROI
            for i, roi in enumerate(app_state.led_rois):
                if roi:
                    is_green = i < app_state.led_num_green
                    label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
                    color = (0, 255, 0) if is_green else (0, 0, 255) # Green/Red for confirmed
                    if i >= app_state.calib_led_roi_index: # 未确认的用橙色
                         color = (0, 165, 255)
                    cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 1)
                    text_y = roi[1] - 5 if roi[1] > 10 else roi[1] + roi[3] + 15
                    cv2.putText(app_state.display_frame, label, (roi[0], text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            # 绘制正在选择的 ROI (蓝色)
            if app_state.current_rect:
                r = app_state.current_rect
                cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

            # 绘制模板预览 (虚线边框，最小遮挡)
            if app_state.template_mode and app_state.template_preview_pos:
                r = app_state.template_preview_pos
                x, y, w, h = r

                # 绘制虚线边框 - 最小化遮挡内容
                dash_length = 8  # 虚线段长度
                gap_length = 6   # 间隔长度
                color = (255, 255, 255)  # 白色，高对比度
                thickness = 1

                # 绘制顶边虚线
                for i in range(0, w, dash_length + gap_length):
                    end_x = min(x + i + dash_length, x + w)
                    cv2.line(app_state.display_frame, (x + i, y), (end_x, y), color, thickness)

                # 绘制底边虚线
                for i in range(0, w, dash_length + gap_length):
                    end_x = min(x + i + dash_length, x + w)
                    cv2.line(app_state.display_frame, (x + i, y + h), (end_x, y + h), color, thickness)

                # 绘制左边虚线
                for i in range(0, h, dash_length + gap_length):
                    end_y = min(y + i + dash_length, y + h)
                    cv2.line(app_state.display_frame, (x, y + i), (x, end_y), color, thickness)

                # 绘制右边虚线
                for i in range(0, h, dash_length + gap_length):
                    end_y = min(y + i + dash_length, y + h)
                    cv2.line(app_state.display_frame, (x + w, y + i), (x + w, end_y), color, thickness)

        # LED ROI 编辑模式
        elif app_state.current_calib_state == CALIB_STATE_LED_EDIT:
            # 绘制所有ROI
            for i, roi in enumerate(app_state.led_rois):
                if roi:
                    is_green = i < app_state.led_num_green
                    label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"

                    # 根据是否选中决定颜色和样式
                    if i == app_state.selected_roi_index:
                        # 选中的ROI用橙色高亮显示
                        color = (0, 165, 255)  # 橙色
                        thickness = 2  # 从3减少到2
                        # 绘制选中标记（四个角的小方块）
                        corner_size = 8
                        x, y, w, h = roi
                        corner_color = (255, 255, 0)  # 黄色角标
                        # 四个角
                        cv2.rectangle(app_state.display_frame, (x-corner_size//2, y-corner_size//2),
                                     (x+corner_size//2, y+corner_size//2), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x+w-corner_size//2, y-corner_size//2),
                                     (x+w+corner_size//2, y+corner_size//2), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x-corner_size//2, y+h-corner_size//2),
                                     (x+corner_size//2, y+h+corner_size//2), corner_color, -1)
                        cv2.rectangle(app_state.display_frame, (x+w-corner_size//2, y+h-corner_size//2),
                                     (x+w+corner_size//2, y+h+corner_size//2), corner_color, -1)
                    else:
                        # 未选中的ROI用正常颜色
                        color = (0, 255, 0) if is_green else (0, 0, 255)  # Green/Red
                        thickness = 1  # 从2减少到1

                    cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, thickness)
                    text_y = roi[1] - 5 if roi[1] > 10 else roi[1] + roi[3] + 15
                    cv2.putText(app_state.display_frame, label, (roi[0], text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                    # 在ROI中心显示数字键提示
                    center_x, center_y = roi[0] + roi[2]//2, roi[1] + roi[3]//2
                    key_hint = str(i + 1)
                    text_size = cv2.getTextSize(key_hint, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    text_x = center_x - text_size[0]//2
                    text_y = center_y + text_size[1]//2
                    # 绘制半透明背景
                    cv2.rectangle(app_state.display_frame, (text_x-2, text_y-text_size[1]-2),
                                 (text_x+text_size[0]+2, text_y+2), (0, 0, 0), -1)
                    cv2.putText(app_state.display_frame, key_hint, (text_x, text_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # LED 样本采集
        elif app_state.current_calib_state in [CALIB_STATE_LED_SAMPLE_OFF, CALIB_STATE_LED_SAMPLE_ON]:
             is_on_state = app_state.current_calib_state == CALIB_STATE_LED_SAMPLE_ON
             for i, roi in enumerate(app_state.led_rois):
                 if roi:
                     is_green = i < app_state.led_num_green
                     # 根据预期状态选择颜色
                     if is_on_state: # 期望 ON
                          color = (0, 255, 0) if is_green else (0, 0, 255) # Bright Green / Bright Red
                     else: # 期望 OFF
                          color = (0, 100, 0) if is_green else (100, 0, 0) # Dark Green / Dark Red
                     cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 2)

        # 数码管 ROI 选择 (Digit 1 or 2)
        elif app_state.current_calib_state in [CALIB_STATE_DIGIT_ROI_SELECT_1, CALIB_STATE_DIGIT_ROI_SELECT_2]:
            # 使用捕捉的 '88' 图像
            if app_state.digit_calibration_image_88 is not None:
                 # 确保 display_frame 是 '88' 图像的副本
                 if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                      app_state.display_frame = app_state.digit_calibration_image_88.copy()

                 # 绘制 Digit 1 (绿色框)
                 if app_state.digit_rois[0]:
                      r = app_state.digit_rois[0]
                      cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (0, 255, 0), 1)
                 # 绘制 Digit 2 (如果已定义，用青色框)
                 if app_state.digit_rois[1] and app_state.current_calib_state == CALIB_STATE_DIGIT_ROI_SELECT_2:
                      r = app_state.digit_rois[1]
                      cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 255, 0), 1)
                 # 绘制正在选择的 ROI (蓝色)
                 if app_state.current_rect:
                     r = app_state.current_rect
                     cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

        # 数码管段 ROI 选择
        elif app_state.current_calib_state == CALIB_STATE_DIGIT_SEGMENT_SELECT:
             if app_state.digit_calibration_image_88 is not None:
                 if not np.array_equal(app_state.display_frame, app_state.digit_calibration_image_88):
                      app_state.display_frame = app_state.digit_calibration_image_88.copy()

                 # 绘制已确认的 Digit ROIs (细绿框)
                 for roi in app_state.digit_rois:
                     if roi: cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), (0, 255, 0), 1)

                 # 绘制当前 Digit 已确认的 Segment ROIs
                 d_idx = app_state.calib_digit_index
                 if 0 <= d_idx < NUM_DIGITS:
                     for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                         if roi:
                             color = (0, 255, 0) if s_idx < app_state.calib_segment_index else (0, 165, 255) # Green (done) vs Orange (pending)
                             cv2.rectangle(app_state.display_frame, (roi[0], roi[1]), (roi[0]+roi[2], roi[1]+roi[3]), color, 1)

                 # 绘制正在选择的 Segment ROI (蓝色)
                 if app_state.current_rect:
                     r = app_state.current_rect
                     cv2.rectangle(app_state.display_frame, (r[0], r[1]), (r[0]+r[2], r[1]+r[3]), (255, 0, 0), 1)

        # 数码管阈值调整
        elif app_state.current_calib_state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD:
            if app_state.digit_background_image_off is not None:
                if not np.array_equal(app_state.display_frame, app_state.digit_background_image_off):
                    app_state.display_frame = app_state.digit_background_image_off.copy()
                try:
                    temp_gray = cv2.cvtColor(app_state.display_frame, cv2.COLOR_BGR2GRAY)
                    for d_idx in range(NUM_DIGITS):
                        if app_state.digit_rois[d_idx]:
                            for s_idx, roi in enumerate(app_state.digit_segment_rois[d_idx]):
                                if roi and isinstance(roi, tuple) and len(roi)==4:
                                    sx, sy, sw, sh = roi
                                    if sw > 0 and sh > 0:
                                        y_end = min(sy + sh, temp_gray.shape[0])
                                        x_end = min(sx + sw, temp_gray.shape[1])
                                        roi_y = max(0, sy)
                                        roi_x = max(0, sx)
                                        if roi_y < y_end and roi_x < x_end:
                                            segment_area = temp_gray[roi_y:y_end, roi_x:x_end]
                                            if segment_area.size > 0:
                                                mean_brightness = cv2.mean(segment_area)[0]
                                                # Red if incorrectly ON, Green if correctly OFF
                                                color = (0, 0, 255) if mean_brightness > app_state.digit_brightness_threshold else (0, 255, 0)
                                                cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), color, 1)
                except Exception as e:
                    print(f"绘制阈值调整状态时出错: {e}")

    # --- 在检测模式下绘制 ---
    elif app_state.current_mode == MODE_DETECTION:
        # 1. 绘制 LED ROI 和状态
        for i, roi in enumerate(app_state.led_rois):
            if not roi or not isinstance(roi, tuple) or len(roi) != 4: continue
            x, y, w, h = roi
            if w <= 0 or h <= 0: continue

            is_on = app_state.led_last_status[i] if i < len(app_state.led_last_status) else False
            is_green_led = (i < app_state.led_num_green)
            led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
            status_text = "ON" if is_on else "OFF"

            if is_green_led:
                text_color = (0, 255, 0) if is_on else (150, 150, 150)
                border_color = (0, 255, 0) if is_on else (0, 0, 150)
            else: # Red LED
                text_color = (0, 0, 255) if is_on else (150, 150, 150)
                border_color = (0, 0, 255) if is_on else (150, 0, 0)

            cv2.rectangle(app_state.display_frame, (x, y), (x + w, y + h), border_color, 1)
            text_y = y - 5 if y > 10 else y + h + 15
            cv2.putText(app_state.display_frame, f"{led_label}:{status_text}", (x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, text_color, 1)

        # 2. 绘制数码管段 ROI 和状态
        for d_idx in range(NUM_DIGITS):
            if app_state.digit_rois[d_idx] is None: continue
            dx, dy, dw, dh = app_state.digit_rois[d_idx]

            rec_char = app_state.digit_last_recognized_chars[d_idx] if d_idx < len(app_state.digit_last_recognized_chars) and app_state.digit_last_recognized_chars[d_idx] is not None else '?'
            missing = app_state.digit_last_missing_segments[d_idx] if d_idx < len(app_state.digit_last_missing_segments) else []
            status = "OK"
            status_color = (0, 255, 0)
            if missing:
                missing_labels = [DIGIT_SEGMENT_LABELS[s] for s in missing]
                status = f"FAIL({','.join(missing_labels)})"
                status_color = (0, 0, 255)
            elif rec_char == '?':
                status = "Unknown"
                status_color = (0, 165, 255)

            # 显示识别的字符
            cv2.putText(app_state.display_frame, f"{rec_char}", (dx + dw // 2 - 10, dy + dh + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, status_color, 2)

            # 绘制每个段的状态
            pattern = app_state.digit_last_segment_patterns[d_idx] if d_idx < len(app_state.digit_last_segment_patterns) else [0]*NUM_SEGMENTS_PER_DIGIT
            for s_idx, seg_roi in enumerate(app_state.digit_segment_rois[d_idx]):
                 if seg_roi and isinstance(seg_roi, tuple) and len(seg_roi)==4:
                     sx, sy, sw, sh = seg_roi
                     if sw <=0 or sh <=0: continue

                     is_seg_on = pattern[s_idx] == 1 if s_idx < len(pattern) else False
                     seg_color = (0, 255, 0) if is_seg_on else (50, 50, 50)
                     thickness = 2 if is_seg_on else 1

                     # 高亮缺失的段
                     is_missing = s_idx in missing
                     if is_missing:
                          seg_color = (0, 255, 255) # Yellow border
                          cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), seg_color, 1)
                          cv2.drawMarker(app_state.display_frame, (sx + sw // 2, sy + sh // 2), (0, 0, 255), cv2.MARKER_CROSS, max(5, min(sw, sh)//2), 1)
                     else:
                          cv2.rectangle(app_state.display_frame, (sx, sy), (sx + sw, sy + sh), seg_color, thickness)


def draw_hud(app_state: AppState):
    """在 display_frame 上绘制状态信息和提示"""
    if app_state.display_frame is None: return

    h, w = app_state.display_frame.shape[:2]
    info_color = (255, 255, 0) # Yellow

    # 根据窗口大小动态调整字体大小
    base_font_scale = 0.35  # 减小基础字体大小
    if w > 1600:  # 高分辨率时稍微增大
        base_font_scale = 0.4
    elif w < 1000:  # 低分辨率时进一步减小
        base_font_scale = 0.3

    font_scale = base_font_scale
    led_font_scale = base_font_scale - 0.05  # LED状态文本稍小
    line_height = 16  # 减小行高以容纳更多内容
    y_offset = 15

    # --- 左上角信息区 ---
    # LED 状态
    if app_state.current_mode == MODE_DETECTION and app_state.led_max_rois > 0:
        cv2.putText(app_state.display_frame, "--- LEDs ---", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, font_scale, info_color, 1)
        y_offset += line_height
        status_texts_led = []
        for i in range(app_state.led_max_rois):
             if not app_state.led_rois[i]: continue # 只显示已定义的 ROI
             is_on = app_state.led_last_status[i] if i < len(app_state.led_last_status) else False
             values = app_state.led_last_values[i] if i < len(app_state.led_last_values) else (0,0,0)
             gray_val, green_val, red_val = values
             is_green_led = (i < app_state.led_num_green)
             led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
             status_text = "ON" if is_on else "OFF"
             # 优化文本格式：缩短标签，减少小数位数
             status_texts_led.append(f"{led_label}: {status_text} (Gy:{gray_val:.0f},Gn:{green_val:.0f},Rd:{red_val:.0f})")

        for txt in status_texts_led:
            if y_offset > h - 80: break  # 增加底部边距
            is_green_led = txt.startswith("G")
            color = (255, 255, 255)
            if ": ON" in txt: color = (0, 255, 0) if is_green_led else (0, 0, 255)
            elif ": OFF" in txt: color = (150, 150, 150)
            cv2.putText(app_state.display_frame, txt, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
            y_offset += line_height

        # LED 阈值显示 (检测模式下) - 分行显示以避免过长
        # 绿色LED阈值
        thresh_text_g = f"G Th: G(g/G)={app_state.led_gray_threshold_green:.1f},Gn(v/V)={app_state.led_green_threshold:.1f}"
        cv2.putText(app_state.display_frame, thresh_text_g, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
        y_offset += line_height
        # 红色LED阈值
        thresh_text_r = f"R Th: G(y/Y)={app_state.led_gray_threshold_red:.1f},Rd(r/R)={app_state.led_red_threshold:.1f}"
        cv2.putText(app_state.display_frame, thresh_text_r, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
        y_offset += line_height + 3

    # 数码管状态 (检测模式下)
    if app_state.current_mode == MODE_DETECTION:
        cv2.putText(app_state.display_frame, "--- Digits ---", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, font_scale, info_color, 1)
        y_offset += line_height
        digit_status_texts = []
        for d_idx in range(NUM_DIGITS):
            if app_state.digit_rois[d_idx] is None: continue # 只显示定义的数字
            rec_char = app_state.digit_last_recognized_chars[d_idx] if d_idx < len(app_state.digit_last_recognized_chars) and app_state.digit_last_recognized_chars[d_idx] is not None else '?'
            missing = app_state.digit_last_missing_segments[d_idx] if d_idx < len(app_state.digit_last_missing_segments) else []
            status = "OK"
            if missing: status = f"FAIL({','.join(DIGIT_SEGMENT_LABELS[s] for s in missing)})"
            elif rec_char == '?': status = "Unknown"
            digit_status_texts.append(f"D{d_idx+1}: {rec_char} [{status}]")

        for txt in digit_status_texts:
             if y_offset > h - 60: break  # 增加底部边距
             color = (0,255,0) # Default Green
             if "[FAIL" in txt: color = (0,0,255) # Red
             elif "[Unknown]" in txt: color = (0,165,255) # Orange
             cv2.putText(app_state.display_frame, txt, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, color, 1)
             y_offset += line_height
        # 数码管阈值显示
        cv2.putText(app_state.display_frame, f"D Th (+/-): {app_state.digit_brightness_threshold:.1f}", (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, led_font_scale, info_color, 1)
        y_offset += line_height

    # --- 右上角模式和 FPS ---
    mode_map = {MODE_CAMERA_SETTINGS: "Settings", MODE_CALIBRATION: "Calibration", MODE_DETECTION: "Detection"}
    mode_str = f"Mode: {mode_map.get(app_state.current_mode, 'Unknown')}"
    if app_state.current_mode == MODE_CALIBRATION:
         mode_str += f" (State: {app_state.current_calib_state})" # 显示校准子状态
    cv2.putText(app_state.display_frame, mode_str, (w - 280, 20), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (255, 255, 0), 1)
    cv2.putText(app_state.display_frame, f"FPS: {app_state.fps:.1f}", (w - 280, 40), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 255, 0), 1)


    # --- 底部状态栏和提示栏 ---
    bottom_font_scale = led_font_scale  # 使用较小的字体
    # 状态消息 (根据模式显示不同内容，或者直接显示 app_state.status_message)
    if app_state.status_message:
         cv2.putText(app_state.display_frame, app_state.status_message, (10, h - 35), cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, (0, 255, 255), 1)
    # 提示消息
    if app_state.prompt_message:
         cv2.putText(app_state.display_frame, app_state.prompt_message, (10, h - 15), cv2.FONT_HERSHEY_SIMPLEX, bottom_font_scale, (255, 255, 255), 1)


# --- Mode Handling Functions ---

def _run_camera_settings_mode(app_state: AppState):
    """处理摄像头参数设置模式的逻辑和按键"""
    if app_state.cap is None or not app_state.cap.isOpened():
        app_state.prompt_message = "Error: Camera not initialized!"
        app_state.status_message = ""
        time.sleep(1) # 短暂停留显示错误
        # 尝试重新初始化？或者退出？
        app_state.running = False # 暂时退出
        return

    ret, frame = app_state.cap.read()
    if not ret or frame is None:
        app_state.prompt_message = "Error: Cannot read frame from camera!"
        app_state.status_message = ""
        # 准备一个黑色背景帧用于显示信息
        h, w = (480, 640) # Default
        if app_state.display_frame is not None: h, w = app_state.display_frame.shape[:2]
        app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
        return
    else:
        # 使用当前帧作为显示帧
        app_state.display_frame = frame.copy()

    # 显示当前设置
    res_w, res_h = RESOLUTION_PRESETS[app_state.current_resolution_index]
    settings_text = [
        f"Resolution: {res_w}x{res_h} ('T'/'t')",
        f"Exposure: {app_state.exposure_value:.1f} ('E'/'e')",
        f"Brightness: {app_state.brightness_value:.1f} ('B'/'b')"
    ]
    app_state.status_message = " | ".join(settings_text)
    app_state.prompt_message = "Press 'S' Save | 'Enter' Calibrate | 'Q' Quit"

    # 处理按键
    key = cv2.waitKey(1) & 0xFF
    needs_reapply = False
    if key == ord('q'):
        app_state.running = False
    elif key == ord('t'): # 降低分辨率
        app_state.current_resolution_index = (app_state.current_resolution_index - 1 + len(RESOLUTION_PRESETS)) % len(RESOLUTION_PRESETS)
        needs_reapply = True
    elif key == ord('T'): # 增加分辨率
        app_state.current_resolution_index = (app_state.current_resolution_index + 1) % len(RESOLUTION_PRESETS)
        needs_reapply = True
    elif key == ord('e'): # 减少曝光
        app_state.exposure_value -= EXPOSURE_STEP
        # 直接应用曝光和亮度设置可能有效，避免完全重新应用所有设置
        if app_state.cap: app_state.cap.set(cv2.CAP_PROP_EXPOSURE, float(app_state.exposure_value))
    elif key == ord('E'): # 增加曝光
        app_state.exposure_value += EXPOSURE_STEP
        if app_state.cap: app_state.cap.set(cv2.CAP_PROP_EXPOSURE, float(app_state.exposure_value))
    elif key == ord('b'): # 减少亮度
        app_state.brightness_value -= BRIGHTNESS_STEP
        if app_state.cap: app_state.cap.set(cv2.CAP_PROP_BRIGHTNESS, float(app_state.brightness_value))
    elif key == ord('B'): # 增加亮度
        app_state.brightness_value += BRIGHTNESS_STEP
        if app_state.cap: app_state.cap.set(cv2.CAP_PROP_BRIGHTNESS, float(app_state.brightness_value))
    elif key == ord('s'):
        # 只保存摄像头设置部分到配置文件
        # TODO: 考虑是否需要一个只保存部分配置的函数，或者总是全量保存
        print("Saving current configuration (including camera settings)...")
        config_manager.save_config(app_state)
        app_state.status_message = "Camera settings saved in config." # 临时反馈

    elif key == 13: # Enter
        print("Camera settings confirmed, entering Calibration mode.")
        # 保存当前配置，因为可能需要这些设置进行校准
        config_manager.save_config(app_state)
        app_state.current_mode = MODE_CALIBRATION
        app_state.current_calib_state = CALIB_STATE_START
        app_state.current_rect = None # 清空可能正在绘制的矩形

    # 如果分辨率改变，重新应用所有设置
    if needs_reapply:
        print("Resolution changed, reapplying all camera settings...")
        # 直接调用 camera_manager 的函数
        if not camera_manager.apply_camera_settings(app_state.cap, app_state):
            app_state.prompt_message = "Error: Failed to apply new camera settings!"
            app_state.status_message = ""
            app_state.running = False # 严重错误


def _run_calibration_mode(app_state: AppState):
    """处理校准模式的逻辑和按键"""
    if app_state.cap is None or not app_state.cap.isOpened():
        app_state.prompt_message = "Error: Camera not initialized for calibration!"
        time.sleep(1)
        app_state.running = False
        return

    # 优先使用校准步骤中捕捉的静态图像，否则使用实时帧
    frame_to_use = None
    if app_state.current_calib_state in [CALIB_STATE_DIGIT_ROI_SELECT_1,
                                         CALIB_STATE_DIGIT_ROI_SELECT_2,
                                         CALIB_STATE_DIGIT_SEGMENT_SELECT] and \
       app_state.digit_calibration_image_88 is not None:
           frame_to_use = app_state.digit_calibration_image_88
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD and \
         app_state.digit_background_image_off is not None:
           frame_to_use = app_state.digit_background_image_off

    # 如果没有静态图像，读取实时帧
    if frame_to_use is None:
        ret, frame = app_state.cap.read()
        if not ret or frame is None:
            app_state.prompt_message = "Error: Cannot read frame during calibration!"
            h, w = (480, 640)
            if app_state.display_frame is not None: h, w = app_state.display_frame.shape[:2]
            app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
            return
        else:
            frame_to_use = frame
            app_state.current_frame = frame_to_use.copy() # 保存原始帧用于采样

    # 准备显示帧
    app_state.display_frame = frame_to_use.copy()

    # --- 根据校准子状态处理逻辑 ---
    key = cv2.waitKey(1) & 0xFF

    # 状态: 开始/选择校准类型
    if app_state.current_calib_state == CALIB_STATE_START:
        # 检查是否有已定义的LED ROI可以编辑
        has_led_rois = any(roi for roi in app_state.led_rois)
        edit_hint = " | 'E' Edit LEDs" if has_led_rois else ""
        resample_hint = " | 'R' Resample LEDs" if has_led_rois else ""
        app_state.prompt_message = f"Calibration: 'B' Base Points | 'L' LED | 'A' Auto Column | 'D' Digit{edit_hint}{resample_hint} | 'S' Save&Exit Calib | 'Enter' Detect (if ready)"

        if key == ord('b'):
            # 进入基准点选择模式
            app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
            app_state.calib_base_point_index = 0
            # 清空旧基准点数据
            base_point_manager.reset_alignment_system(app_state)
            print("开始基准点校准。请点击产品上的明显特征点（如螺丝孔、标记、边角等）")
        elif key == ord('l'):
            # 重置 LED 校准进度和数据
            app_state.calib_led_roi_index = 0
            app_state.led_rois = [None] * app_state.led_max_rois # 清空旧 ROI
            app_state.led_off_state_samples = [[] for _ in range(app_state.led_max_rois)] # 清空样本
            app_state.led_on_state_samples = [[] for _ in range(app_state.led_max_rois)]
            app_state.is_quick_resample_mode = False  # 重置快速重新采样标记
            app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
            print(f"开始校准 {app_state.led_num_green} 绿 和 {app_state.led_num_red} 红 LED...")
        elif key == ord('a'):
            # 进入列自动生成模式
            app_state.current_calib_state = CALIB_STATE_LED_COLUMN_AUTO
            app_state.column_auto_step = 0
            app_state.column_first_led = None
            app_state.column_second_led = None
            app_state.column_generated_rois = []
            app_state.column_roi_start_index = 0
            print("进入列自动生成模式。请点击第一列的第1个LED...")
        elif key == ord('r') and has_led_rois:
            # 快速重新采样：保留ROI坐标，只重新采集样本和阈值
            app_state.led_off_state_samples = [[] for _ in range(app_state.led_max_rois)] # 清空样本
            app_state.led_on_state_samples = [[] for _ in range(app_state.led_max_rois)]
            app_state.is_quick_resample_mode = True  # 标记为快速重新采样模式
            app_state.current_calib_state = CALIB_STATE_LED_SAMPLE_OFF
            print(f"快速重新采样模式：保留现有ROI坐标，重新采集 {app_state.led_num_green} 绿 和 {app_state.led_num_red} 红 LED 的亮灭样本...")
            print("提示：这将只更新样本数据和阈值，ROI坐标保持不变")
        elif key == ord('e') and has_led_rois:
            # 进入LED ROI编辑模式
            app_state.current_calib_state = CALIB_STATE_LED_EDIT
            app_state.selected_roi_index = -1  # 重置选择
            app_state.moving_roi = False
            print("进入LED ROI编辑模式。点击ROI或按数字键选择要移动的ROI。")
        elif key == ord('d'):
            # 重置数码管校准进度和数据
            app_state.calib_digit_index = 0
            app_state.calib_segment_index = 0
            app_state.digit_rois = [None] * NUM_DIGITS
            app_state.digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
            app_state.digit_calibration_image_88 = None
            app_state.digit_background_image_off = None
            app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_88
        elif key == ord('s'):
             print("保存当前配置并退出校准模式...")
             config_manager.save_config(app_state)
             app_state.current_mode = MODE_DETECTION # 退出到检测模式
        elif key == 13: # Enter to Detect
             # 检查校准是否完整
             num_defined_led_rois = sum(1 for r in app_state.led_rois if r)
             led_samples_ok = all(app_state.led_off_state_samples[i] and app_state.led_on_state_samples[i] for i, r in enumerate(app_state.led_rois) if r)
             led_ok = (app_state.led_max_rois == 0) or (num_defined_led_rois == app_state.led_max_rois and led_samples_ok)
             dig_ok = all(r for r in app_state.digit_rois) and all(all(s for s in d) for d in app_state.digit_segment_rois)

             if led_ok and dig_ok:
                 print("校准数据完整，进入检测模式...")
                 config_manager.save_config(app_state) # 保存最终校准结果
                 app_state.current_mode = MODE_DETECTION
             else:
                 missing = []
                 if not led_ok: missing.append(f"LEDs (ROIs:{num_defined_led_rois}/{app_state.led_max_rois}, Samples OK:{led_samples_ok})")
                 if not dig_ok: missing.append(f"Digits (ROIs:{sum(1 for r in app_state.digit_rois if r)}/2, Segments:{sum(sum(1 for s in d if s) for d in app_state.digit_segment_rois)}/14)")
                 app_state.prompt_message = f"Incomplete: {', '.join(missing)}. Calibrate first."

    # 状态: 基准点选择
    elif app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
        idx = app_state.calib_base_point_index

        if idx < 2:
            app_state.status_message = f"Base Point Calib: Select Feature Point {idx + 1}/2"
            app_state.prompt_message = "Click on clear feature points (screws/marks/corners) | 'R' Reset | 'Esc' Skip"
        else:
            app_state.status_message = "Base Point Selection Complete"
            app_state.prompt_message = "'Enter' Continue LED Calib | 'R' Reset Base Points | 'Esc' Skip Base Point Function"

        if key == 13 and idx >= 2:  # Enter继续
            app_state.original_base_points = app_state.base_points.copy()
            # 保存当前所有ROI坐标作为原始坐标
            base_point_manager.save_original_roi_coordinates(app_state)

            # 验证基准点系统健康状态
            is_healthy, issues = base_point_manager.validate_alignment_system(app_state)
            if is_healthy:
                print("✓ 基准点校准完成，系统验证通过")
            else:
                print("⚠ 基准点校准完成，但发现以下问题:")
                for issue in issues:
                    print(f"  - {issue}")
                print("建议重新选择基准点或检查配置")

            app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
            print("开始LED ROI选择")
        elif key == ord('r'):  # 重选
            app_state.calib_base_point_index = 0
            base_point_manager.reset_alignment_system(app_state)
            print("重新开始基准点选择")
        elif key == 27:  # Esc跳过基准点功能
            app_state.alignment_enabled = False
            app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
            print("跳过基准点校准，ROI将使用固定坐标模式")

    # 状态: LED ROI 选择
    elif app_state.current_calib_state == CALIB_STATE_LED_ROI_SELECT:
        idx = app_state.calib_led_roi_index
        if idx >= app_state.led_max_rois:
             print("所有 LED ROI 选择/跳过完成。")
             app_state.current_calib_state = CALIB_STATE_LED_SAMPLE_OFF
             return # 进入下一状态

        roi_type = "Green" if idx < app_state.led_num_green else "Red"
        app_state.status_message = f"LED Calib: Select ROI {idx + 1}/{app_state.led_max_rois} ({roi_type})"

        # 根据是否在模板模式显示不同的提示
        if app_state.template_mode:
            app_state.prompt_message = "Template Mode: Click to place | 'Enter' Confirm & Continue | 'Esc' Exit Template | 'R' Reset"
        else:
            # 检查是否可以使用模板功能（需要有前一个ROI作为模板）
            can_use_template = idx > 0 and app_state.led_rois[idx-1] is not None
            template_hint = " | 'T' Template" if can_use_template else ""
            app_state.prompt_message = f"Drag mouse. 'Enter' Confirm | 'N' Next/Skip | 'B' Back{template_hint} | 'R' Reset | Esc Back"

        if key == 13 and app_state.current_rect: # Enter 确认
            app_state.led_rois[idx] = app_state.current_rect
            # ⭐ 同时保存到原始坐标
            app_state.original_led_rois[idx] = app_state.current_rect
            print(f"{roi_type} LED ROI {idx + 1} 确认: {app_state.current_rect}")
            app_state.current_rect = None
            app_state.calib_led_roi_index += 1
            # 确认后保持模板模式，实现连续复制
            if app_state.template_mode:
                print("模板模式保持激活，可继续复制下一个ROI")
        elif key == ord('n'): # 跳过
             app_state.led_rois[idx] = None # 明确设为 None
             print(f"跳过 LED ROI {idx + 1}.")
             app_state.current_rect = None
             app_state.calib_led_roi_index += 1
             # 跳过后也退出模板模式
             if app_state.template_mode:
                 app_state.template_mode = False
                 app_state.template_preview_pos = None
                 print("已退出模板模式")
        elif key == ord('b') and app_state.calib_led_roi_index > 0: # 回退
            app_state.calib_led_roi_index -= 1
            # 清空要重新选择的ROI
            app_state.led_rois[app_state.calib_led_roi_index] = None
            app_state.current_rect = None
            prev_roi_type = "Green" if app_state.calib_led_roi_index < app_state.led_num_green else "Red"
            print(f"回退到 {prev_roi_type} LED ROI {app_state.calib_led_roi_index + 1}")
            # 回退时退出模板模式
            app_state.template_mode = False
            app_state.template_preview_pos = None
        elif key == ord('t') and not app_state.template_mode: # 进入模板模式
            # 检查是否有前一个ROI可以作为模板
            if idx > 0 and app_state.led_rois[idx-1] is not None:
                template_roi = app_state.led_rois[idx-1]
                app_state.template_roi = (template_roi[2], template_roi[3])  # 只保存宽高
                app_state.template_mode = True
                app_state.current_rect = None  # 清空当前选择
                print(f"进入模板复制模式，模板大小: {app_state.template_roi[0]}x{app_state.template_roi[1]}")
            else:
                print("无法使用模板功能：需要先选择至少一个ROI作为模板")
        elif key == ord('r'): # 重置
            app_state.led_rois = [None] * app_state.led_max_rois
            app_state.calib_led_roi_index = 0
            app_state.current_rect = None
            app_state.led_off_state_samples = [[] for _ in range(app_state.led_max_rois)]
            app_state.led_on_state_samples = [[] for _ in range(app_state.led_max_rois)]
            # 重置时也清空模板状态
            app_state.template_mode = False
            app_state.template_roi = None
            app_state.template_preview_pos = None
            print("所有 LED ROI 和样本已重置。")
        elif key == 27: # Esc
            if app_state.template_mode:
                # 在模板模式下，Esc 退出模板模式
                app_state.template_mode = False
                app_state.template_preview_pos = None
                app_state.current_rect = None
                print("已退出模板模式")
            else:
                # 正常模式下，Esc 返回上一级
                app_state.current_calib_state = CALIB_STATE_START

    # 状态: LED 灭灯样本采集
    elif app_state.current_calib_state == CALIB_STATE_LED_SAMPLE_OFF:
        valid_rois_count = sum(1 for roi in app_state.led_rois if roi)
        if valid_rois_count == 0:
             app_state.prompt_message = "No valid LED ROI, cannot sample. Esc to return to selection."
             app_state.status_message = "LED Calib: Error - No valid ROI"
        else:
             app_state.prompt_message = f"Ensure all {valid_rois_count} defined LEDs are OFF. Press 'C' to capture. Esc to go back."
             app_state.status_message = "LED Calib: Capture OFF samples"

        if key == ord('c') and valid_rois_count > 0:
            # 使用原始帧进行采样
            if app_state.current_frame is not None:
                # 调用 led_detector 中的采样函数 (这里改为直接操作 app_state)
                samples = led_detector.led_capture_samples(app_state)
                if samples is not None: # 检查采集是否成功 (或部分成功)
                    # 检查是否所有有效 ROI 都采集到了
                    samples_collected_for_valid = all(samples[i] for i, r in enumerate(app_state.led_rois) if r)
                    if samples_collected_for_valid:
                        app_state.led_off_state_samples = samples
                        print("灭灯样本采集成功。")
                        app_state.current_calib_state = CALIB_STATE_LED_SAMPLE_ON
                    else:
                        app_state.prompt_message = "Warning: Failed to capture OFF samples for all valid ROIs. Please retry. Esc to return"
                        # 可以选择性地保存部分样本，或者要求全部成功
                        # app_state.led_off_state_samples = samples # 保存部分结果
                else:
                    app_state.prompt_message = "OFF sample capture failed or camera error. Please retry. Esc to return"
            else:
                 app_state.prompt_message = "Error: Cannot get current frame for sampling. Esc to return"
        elif key == 27: # Esc
            app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
            # 重置索引到第一个未定义的或最后一个 ROI
            first_undefined = next((i for i, r in enumerate(app_state.led_rois) if r is None), app_state.led_max_rois)
            app_state.calib_led_roi_index = min(first_undefined, app_state.led_max_rois)

    # 状态: LED 亮灯样本采集
    elif app_state.current_calib_state == CALIB_STATE_LED_SAMPLE_ON:
        valid_rois_count = sum(1 for roi in app_state.led_rois if roi)
        if valid_rois_count == 0:
             app_state.prompt_message = "No valid LED ROI. Esc to return."
             app_state.status_message = "LED Calib: Error - No valid ROI"
        else:
             app_state.prompt_message = f"Ensure all {valid_rois_count} defined LEDs are ON. Press 'C' to capture. Esc to go back."
             app_state.status_message = "LED Calib: Capture ON samples"

        if key == ord('c') and valid_rois_count > 0:
             if app_state.current_frame is not None:
                 samples = led_detector.led_capture_samples(app_state)
                 if samples is not None:
                      samples_collected_for_valid = all(samples[i] for i, r in enumerate(app_state.led_rois) if r)
                      if samples_collected_for_valid:
                           app_state.led_on_state_samples = samples
                           print("亮灯样本采集成功。")
                           app_state.current_calib_state = CALIB_STATE_LED_ANALYZE
                      else:
                           app_state.prompt_message = "Warning: Failed to capture ON samples for all valid ROIs. Please retry. Esc to return"
                           # app_state.led_on_state_samples = samples # 保存部分
                 else:
                      app_state.prompt_message = "ON sample capture failed or camera error. Please retry. Esc to return"
             else:
                  app_state.prompt_message = "Error: Cannot get current frame for sampling. Esc to return"
        elif key == 27: # Esc
            app_state.current_calib_state = CALIB_STATE_LED_SAMPLE_OFF

    # 状态: LED 分析样本
    elif app_state.current_calib_state == CALIB_STATE_LED_ANALYZE:
        app_state.status_message = "LED Calib: Analyze samples and calculate thresholds"
        num_defined_rois = sum(1 for r in app_state.led_rois if r)
        samples_valid = all(app_state.led_off_state_samples[i] and app_state.led_on_state_samples[i] for i, r in enumerate(app_state.led_rois) if r)

        if num_defined_rois == 0:
            app_state.prompt_message = "No defined LED ROI, cannot analyze. Esc to return"
        elif not samples_valid:
            app_state.prompt_message = "Samples incomplete (not covering all defined ROIs), cannot analyze. Esc to return"
        else:
            app_state.prompt_message = "Press 'A' to analyze and calculate thresholds | Esc to go back"

        if key == ord('a') and num_defined_rois > 0 and samples_valid:
            led_detector.led_calculate_thresholds(app_state) # 函数内部会更新 app_state 的阈值
            print(f"LED 阈值计算完成: G(G={app_state.led_gray_threshold_green:.1f}, Gn={app_state.led_green_threshold:.1f}), R(G={app_state.led_gray_threshold_red:.1f}, Rd={app_state.led_red_threshold:.1f})")

            # 根据模式选择保存方式
            if app_state.is_quick_resample_mode:
                # 快速重新采样模式：只更新样本和阈值
                if config_manager.save_led_samples_and_thresholds_only(app_state):
                    print("✓ 快速重新采样完成！样本数据和阈值已更新，ROI坐标保持不变。")
                else:
                    print("✗ 快速更新失败，将使用完整保存...")
                    config_manager.save_config(app_state)
                app_state.is_quick_resample_mode = False  # 重置标记
            else:
                # 完整校准模式：保存所有配置
                config_manager.save_config(app_state)
                print("LED 校准完成！可以按 'E' 进入编辑模式调整ROI位置。")

            app_state.current_calib_state = CALIB_STATE_START # 返回校准主菜单
        elif key == 27: # Esc
             app_state.current_calib_state = CALIB_STATE_LED_SAMPLE_ON # 返回亮灯采样

    # 状态: LED ROI 编辑模式
    elif app_state.current_calib_state == CALIB_STATE_LED_EDIT:
        app_state.status_message = "LED Edit: Adjust ROI positions"

        # 显示当前选中的ROI信息
        if app_state.selected_roi_index != -1:
            is_green = app_state.selected_roi_index < app_state.led_num_green
            led_label = f"G{app_state.selected_roi_index+1}" if is_green else f"R{app_state.selected_roi_index - app_state.led_num_green + 1}"
            app_state.prompt_message = f"Selected: {led_label} | Drag to move | '1'-'6' Select ROI | 'Enter' Confirm | 'Esc' Exit Edit"
        else:
            app_state.prompt_message = "Click ROI or press '1'-'6' to select | 'Enter' Confirm All | 'Esc' Exit Edit"

        # 数字键选择ROI
        if ord('1') <= key <= ord('6'):
            roi_index = key - ord('1')
            if roi_index < app_state.led_max_rois and app_state.led_rois[roi_index] is not None:
                app_state.selected_roi_index = roi_index
                app_state.moving_roi = False  # 重置移动状态
                is_green = roi_index < app_state.led_num_green
                led_label = f"G{roi_index+1}" if is_green else f"R{roi_index - app_state.led_num_green + 1}"
                print(f"选中 {led_label} ROI，可以拖拽移动")
            else:
                print(f"ROI {roi_index + 1} 不存在或未定义")

        elif key == 13: # Enter - 确认所有更改并保存
            config_manager.save_config(app_state)
            print("ROI位置已保存")
            app_state.current_calib_state = CALIB_STATE_START
            # 清理编辑状态
            app_state.selected_roi_index = -1
            app_state.moving_roi = False
            app_state.move_start_pos = None
            app_state.original_roi_pos = None

        elif key == 27: # Esc - 退出编辑模式
            app_state.current_calib_state = CALIB_STATE_START
            # 清理编辑状态
            app_state.selected_roi_index = -1
            app_state.moving_roi = False
            app_state.move_start_pos = None
            app_state.original_roi_pos = None
            print("退出LED ROI编辑模式")

    # --- 数码管校准流程 ---
    # 状态: 捕捉 '88' 图像
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_CAPTURE_88:
         app_state.prompt_message = "Digit: Display '88'. Press 'C' to capture image. Esc to return to main menu"
         app_state.status_message = "Digit: Capture '88' Image"
         if key == ord('c'):
             if app_state.current_frame is not None:
                 app_state.digit_calibration_image_88 = app_state.current_frame.copy()
                 print("数码管 '88' 图像已捕捉。")
                 app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1
                 app_state.current_rect = None
             else:
                 app_state.prompt_message = "Error: Cannot capture image, current frame is invalid."
         elif key == 27: # Esc
             app_state.current_calib_state = CALIB_STATE_START

    # 状态: 选择 Digit 1 ROI
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_ROI_SELECT_1:
        if app_state.digit_calibration_image_88 is None:
             app_state.prompt_message = "Error: '88' image not captured! Press Esc to return"
             if key == 27: app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_88
             return
        app_state.status_message = "Digit: Select Left Digit Area (Digit 1)"
        app_state.prompt_message = "Drag to select. 'Enter' Confirm. Esc to go back"
        if key == 13 and app_state.current_rect:
            app_state.digit_rois[0] = app_state.current_rect
            # ⭐ 同时保存到原始坐标
            app_state.original_digit_rois[0] = app_state.current_rect
            print(f"数码管 Digit 1 ROI 确认: {app_state.current_rect}")
            app_state.current_rect = None
            app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_2
        elif key == 27:
            app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_88

    # 状态: 选择 Digit 2 ROI
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_ROI_SELECT_2:
         if app_state.digit_calibration_image_88 is None:
              app_state.prompt_message = "Error: '88' image not captured! Press Esc to return"
              if key == 27: app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1
              return
         app_state.status_message = "Digit: Select Right Digit Area (Digit 2)"
         app_state.prompt_message = "Drag to select. 'Enter' Confirm. Esc to go back"
         if key == 13 and app_state.current_rect:
             app_state.digit_rois[1] = app_state.current_rect
             # ⭐ 同时保存到原始坐标
             app_state.original_digit_rois[1] = app_state.current_rect
             print(f"数码管 Digit 2 ROI 确认: {app_state.current_rect}")
             app_state.current_rect = None
             # 重置段 ROI 和索引，准备开始选择段
             app_state.digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
             app_state.calib_digit_index = 0
             app_state.calib_segment_index = 0
             app_state.current_calib_state = CALIB_STATE_DIGIT_SEGMENT_SELECT
         elif key == 27:
             app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1

    # 状态: 选择数码管段 ROI
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_SEGMENT_SELECT:
         if app_state.digit_calibration_image_88 is None:
              app_state.prompt_message = "Error: '88' image lost! Press Esc to return"
              if key == 27: app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_88
              return
         # 确保数字 ROI 已定义
         if app_state.digit_rois[0] is None or app_state.digit_rois[1] is None:
              app_state.prompt_message = "Error: Digit ROI not defined! Esc to return to digit selection"
              if key == 27: app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1
              return

         d_idx = app_state.calib_digit_index
         s_idx = app_state.calib_segment_index
         segment_label = DIGIT_SEGMENT_LABELS[s_idx]
         app_state.status_message = f"Digit: Digit {d_idx + 1}, Select Segment '{segment_label.upper()}' ({s_idx+1}/7)"
         app_state.prompt_message = "Drag mouse. 'Enter' Confirm, 'N' Skip, 'P' Prev, Esc Back to Digit ROI"

         if key == 13 and app_state.current_rect: # Enter 确认
             app_state.digit_segment_rois[d_idx][s_idx] = app_state.current_rect
             # ⭐ 同时保存到原始坐标
             app_state.original_digit_segment_rois[d_idx][s_idx] = app_state.current_rect
             print(f"  Digit {d_idx+1}, Segment {segment_label} ROI: {app_state.current_rect}")
             app_state.current_rect = None
             # 移动到下一个
             app_state.calib_segment_index += 1
             if app_state.calib_segment_index >= NUM_SEGMENTS_PER_DIGIT:
                 app_state.calib_segment_index = 0
                 app_state.calib_digit_index += 1
                 if app_state.calib_digit_index >= NUM_DIGITS:
                     print("所有数码管段选择完成。")
                     app_state.current_rect = None # 清除可能残留的选择框
                     app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_BG
                 else:
                     print(f"--- 开始校准 Digit {app_state.calib_digit_index + 1} 的段 ---")
         elif key == ord('n'): # 跳过
              app_state.digit_segment_rois[d_idx][s_idx] = None
              print(f"  Digit {d_idx+1}, Skipping Segment {segment_label}")
              app_state.current_rect = None
              app_state.calib_segment_index += 1
              if app_state.calib_segment_index >= NUM_SEGMENTS_PER_DIGIT:
                  app_state.calib_segment_index = 0
                  app_state.calib_digit_index += 1
                  if app_state.calib_digit_index >= NUM_DIGITS:
                       print("所有数码管段处理完成（部分可能跳过）。")
                       app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_BG
                  else:
                       print(f"--- 开始校准 Digit {app_state.calib_digit_index + 1} 的段 ---")
         elif key == ord('p'): # 上一个
              app_state.current_rect = None
              app_state.calib_segment_index -= 1
              if app_state.calib_segment_index < 0:
                   app_state.calib_digit_index -= 1
                   if app_state.calib_digit_index < 0:
                        app_state.calib_digit_index = 0
                        app_state.calib_segment_index = 0
                        print("已在第一个数字的第一个段。")
                   else:
                        app_state.calib_segment_index = NUM_SEGMENTS_PER_DIGIT - 1 # 上个数字最后一段
                        print(f"--- 返回校准 Digit {app_state.calib_digit_index + 1} 的最后一段 ---")
              print(f"返回到 Digit {app_state.calib_digit_index+1}, Segment {DIGIT_SEGMENT_LABELS[app_state.calib_segment_index]}")
         elif key == 27: # Esc
             app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1 # 返回选择 Digit 1

    # 状态: 捕捉背景图像
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_CAPTURE_BG:
         segments_defined = sum(sum(1 for s in d if s) for d in app_state.digit_segment_rois)
         if segments_defined == 0:
              app_state.prompt_message = "Error: No segment ROI defined. Esc to return to segment selection."
              if key == 27:
                   app_state.calib_digit_index = 0
                   app_state.calib_segment_index = 0
                   app_state.current_calib_state = CALIB_STATE_DIGIT_SEGMENT_SELECT
              return
         app_state.prompt_message = "Digit: Turn off all segments. Press 'C' to capture background. Esc to go back"
         app_state.status_message = "Digit: Capture Background Image"
         if key == ord('c'):
             if app_state.current_frame is not None:
                 app_state.digit_background_image_off = app_state.current_frame.copy()
                 print("数码管背景图像已捕捉。")
                 app_state.current_calib_state = CALIB_STATE_DIGIT_ADJUST_THRESHOLD
             else:
                 app_state.prompt_message = "Error: Cannot capture background, current frame is invalid."
         elif key == 27:
             app_state.calib_digit_index = 0
             app_state.calib_segment_index = 0
             app_state.current_calib_state = CALIB_STATE_DIGIT_SEGMENT_SELECT

    # 状态: 调整数码管亮度阈值
    elif app_state.current_calib_state == CALIB_STATE_DIGIT_ADJUST_THRESHOLD:
        if app_state.digit_background_image_off is None:
            app_state.prompt_message = "Error: Background image lost! Press Esc to return"
            if key == 27: app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_BG
            return
        app_state.status_message = f"Digit: Adjust Brightness Threshold (Current: {app_state.digit_brightness_threshold:.1f})"
        app_state.prompt_message = "Use '+' to increase, '-' to decrease. 'Enter' Confirm | Esc to go back"

        if key == ord('+') or key == ord('='):
             app_state.digit_brightness_threshold += DIGIT_THRESHOLD_STEP
        elif key == ord('-') or key == ord('_'):
             app_state.digit_brightness_threshold = max(0.0, app_state.digit_brightness_threshold - DIGIT_THRESHOLD_STEP)
        elif key == 13: # Enter
             print(f"数码管亮度阈值最终设置为: {app_state.digit_brightness_threshold:.1f}")
             print("数码管校准完成！")
             config_manager.save_config(app_state) # 保存最终配置
             app_state.current_calib_state = CALIB_STATE_START # 返回校准主菜单
        elif key == 27: # Esc
             app_state.current_calib_state = CALIB_STATE_DIGIT_CAPTURE_BG

    # 未知状态处理
    else:
        print(f"错误：未知的校准状态 {app_state.current_calib_state}，重置到开始。")
        app_state.current_calib_state = CALIB_STATE_START


def _run_detection_mode(app_state: AppState):
    """处理检测模式的逻辑和按键，包含 "88" 触发的分析流程"""
    if app_state.cap is None or not app_state.cap.isOpened():
        app_state.prompt_message = "Error: Camera not initialized for detection!"
        app_state.running = False
        return

    ret, frame = app_state.cap.read()
    if not ret or frame is None:
        app_state.prompt_message = "Error: Cannot read frame in detection mode!"
        # 使用黑色背景显示错误
        h, w = (480, 640)
        if app_state.display_frame is not None: h, w = app_state.display_frame.shape[:2]
        app_state.display_frame = np.zeros((h, w, 3), dtype=np.uint8)
        return

    # 将原始帧用于检测
    original_frame = frame.copy()
    # 显示帧也从当前帧开始
    app_state.display_frame = frame.copy()

    # 保存当前帧到AppState，供基准点提取使用
    app_state.current_frame = original_frame

    # ⭐ ROI自动对齐
    try:
        alignment_success = base_point_manager.auto_align_rois(app_state, original_frame)

        # 更新对齐状态显示
        if app_state.alignment_enabled:
            if alignment_success:
                if app_state.alignment_fail_count == 0:
                    alignment_status = " | Base Alignment: True"
                else:
                    alignment_status = f" | Base Alignment: Recovered"
            else:
                if app_state.alignment_fail_count < BASE_ALIGNMENT_TIMEOUT:
                    alignment_status = f" | Base Alignment: Searching({app_state.alignment_fail_count})"
                else:
                    alignment_status = f" | Base Alignment: Failed({app_state.alignment_fail_count})"
        else:
            alignment_status = " | Base Alignment: Disabled"

        # 更新状态消息
        if hasattr(app_state, 'status_message') and app_state.status_message:
            app_state.status_message += alignment_status
        else:
            app_state.status_message = f"Detection Mode{alignment_status}"

    except Exception as e:
        logging.error(f"ROI自动对齐时发生异常: {e}")
        app_state.status_message = f"Detection Mode | Base Alignment: Error"

    # --- 执行常规检测 --- (LED 和 Digit)
    if app_state.led_max_rois > 0:
        led_detector.detect_led_status(original_frame, app_state)

    # --- 数码管检测 ---
    recognized_chars, segment_patterns, _ = digit_detector.detect_digit_status(original_frame, app_state)
    # 组合识别结果 (假设有两个数码管)
    current_display = ""
    if len(recognized_chars) >= 2:
        char1 = recognized_chars[0] if recognized_chars[0] is not None else ''
        char2 = recognized_chars[1] if recognized_chars[1] is not None else ''
        current_display = f"{char1}{char2}"
        # 添加详细调试日志
        logging.debug(f"数码管识别结果: 左侧='{char1}', 右侧='{char2}', 组合='{current_display}'")
        logging.debug(f"左侧段模式: {segment_patterns[0]}, 右侧段模式: {segment_patterns[1]}")

    # --- 实现 "88" 和 "1P" 触发的状态机 --- #
    logger = logging.getLogger() # 获取 logger 实例
    current_time = time.time()

    # --- IDLE State: 等待触发 ---
    if app_state.led_analysis_state == 'IDLE':
        # 检查 "88" 触发
        if current_display == "88":
            if app_state.log_file_handler:
                logging.info(f"Detected '88', starting {app_state.logging_duration}s logging process.")
                try:
                    # 清空旧日志文件（开始新一轮记录前）
                    with open(LOG_FILE, 'w', encoding='utf-8') as f:
                        pass
                    logging.info(f"Log file '{LOG_FILE}' cleared before starting.")
                    logger.addHandler(app_state.log_file_handler) # 激活日志记录
                    logging.info("FileHandler added to logger.") # 添加 handler 日志
                    app_state.logging_start_time = current_time
                    app_state.led_analysis_state = 'LOGGING'
                except OSError as e:
                    print(f"Error clearing or starting log file: {e}")
                    logging.error(f"Error clearing or starting log file: {e}")
                except Exception as e:
                    print(f"Error adding log handler: {e}")
                    logging.error(f"Error adding log handler: {e}")
            else:
                print("Error: Log file handler not initialized.")
                logging.error("Cannot start logging for '88': Log file handler not initialized.")

        # 检查IP/1P触发 - 同时检测多种可能的组合
        elif current_display in ["IP", "1P", "iP", "Ip"]:
            trigger_text = current_display
            logging.info(f"Detected '{trigger_text}', sending signal to CPU address 11.")
            send_value_to_cpu(value=1, address=11)
            # IP 信号发送后通常不需要改变状态，继续保持 IDLE 等待下一次触发

    # --- LOGGING State: 记录日志 ---
    elif app_state.led_analysis_state == 'LOGGING':
        elapsed_time = current_time - app_state.logging_start_time
        app_state.status_message = f"Logging for '88' analysis... {elapsed_time:.1f}s / {app_state.logging_duration}s"
        if elapsed_time >= app_state.logging_duration:
            logging.info(f"{app_state.logging_duration}s logging finished. Stopping file handler.")
            if app_state.log_file_handler:
                try:
                    logger.removeHandler(app_state.log_file_handler)
                    logging.info("FileHandler removed from logger.") # 添加 handler 日志
                except Exception as e:
                    print(f"Error removing log handler: {e}")
                    logging.error(f"Error removing log handler: {e}")
            app_state.led_analysis_state = 'WAITING_TO_SIGNAL_12'

    # --- WAITING_TO_SIGNAL_12 State: 发送日志完成信号 ---
    elif app_state.led_analysis_state == 'WAITING_TO_SIGNAL_12':
        logging.info("Sending logging complete signal (1 to addr 12) to CPU.")
        if send_value_to_cpu(value=1, address=12):
            app_state.led_analysis_state = 'ANALYZING'
            logging.info("Signal 1 to addr 12 sent successfully. Starting analysis.")
        else:
            logging.error("Failed to send signal (1 to addr 12). Retrying.")
            # 状态保持不变，下次循环会重试发送

    # --- ANALYZING State: 执行分析脚本 ---
    elif app_state.led_analysis_state == 'ANALYZING':
        app_state.status_message = "Analyzing log file..."
        logging.info("Running LED log analysis...")
        
        # 检查导入是否成功
        if not ANALYSIS_IMPORT_SUCCESS:
            logging.error("Cannot run analysis: analyze_led_cycles function not imported.")
            app_state.led_analysis_state = 'CLEARING'
            return
            
        try:
            # 构造正确的日志文件路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe，使用exe所在目录
                log_dir = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境，使用当前文件所在目录
                log_dir = os.path.dirname(os.path.abspath(__file__))
            
            log_file_path = os.path.join(log_dir, LOG_FILE)
            
            # 添加调试信息
            logging.info(f"Analysis log file path: {log_file_path}")
            logging.info(f"Log file exists: {os.path.exists(log_file_path)}")
            if os.path.exists(log_file_path):
                file_size = os.path.getsize(log_file_path)
                logging.info(f"Log file size: {file_size} bytes")
            
            # 直接调用分析函数，传入正确的日志文件路径
            analysis_result = analyze_led_cycles(log_file_path)
            
            logging.info(f"Analysis function returned: {type(analysis_result)}")
            
            if analysis_result:
                found_cycles = analysis_result['perfect_cycles']
                special_leds_status = analysis_result['special_leds_status']
                
                app_state.last_analysis_result = found_cycles
                app_state.last_special_leds_result = special_leds_status

                logging.info(f"Analysis complete. Found {found_cycles} perfect cycles.")
                logging.info(f"Special LEDs (G33/R1/R2) status: {special_leds_status}")

                # 记录详细的G33/R1/R2分析结果到日志
                g33_result = analysis_result['g33_result']
                r1_result = analysis_result['r1_result']
                r2_result = analysis_result['r2_result']
                
                logging.info(f"G33: {g33_result['total_duration']:.3f}s ({'GOOD' if g33_result['is_good'] else 'BAD'})")
                logging.info(f"R1: {r1_result['total_duration']:.3f}s ({'GOOD' if r1_result['is_good'] else 'BAD'})")
                logging.info(f"R2: {r2_result['total_duration']:.3f}s ({'GOOD' if r2_result['is_good'] else 'BAD'})")

                app_state.led_analysis_state = 'SENDING_RESULT_10'
            else:
                logging.error("Analysis function returned None or empty result.")
                app_state.led_analysis_state = 'CLEARING'

        except Exception as e:
            logging.error(f"Error running analysis function: {e}")
            logging.error(f"Full traceback: {traceback.format_exc()}")
            print(f"Error running analysis function: {e}")
            app_state.led_analysis_state = 'CLEARING'

    # --- SENDING_RESULT_10 State: 根据分析结果发送信号 ---
    elif app_state.led_analysis_state == 'SENDING_RESULT_10':
        if app_state.last_analysis_result is not None:
            value_to_send = 3 if app_state.last_analysis_result == 0 else 1
            logging.info(f"Sending analysis result ({value_to_send} to addr 10) to CPU.")
            if send_value_to_cpu(value=value_to_send, address=10):
                app_state.led_analysis_state = 'SENDING_RESULT_13'
                logging.info(f"Signal {value_to_send} to addr 10 sent successfully. Moving to send M13.")
            else:
                logging.error(f"Failed to send result ({value_to_send} to addr 10). Retrying.")
                # 状态保持不变，下次循环重试
        else:
            # 如果 last_analysis_result 是 None (可能因为解析失败)，直接清理
            logging.warning("No analysis result found, skipping sending to address 10.")
            app_state.led_analysis_state = 'CLEARING'

    # --- SENDING_RESULT_13 State: 发送G33/R1/R2监控结果 ---
    elif app_state.led_analysis_state == 'SENDING_RESULT_13':
        if app_state.last_special_leds_result is not None:
            # GOOD -> 发送1, BAD -> 发送3
            value_to_send = 1 if app_state.last_special_leds_result == 'GOOD' else 3
            logging.info(f"Sending special LEDs result ({value_to_send} to addr 13) to CPU. Status: {app_state.last_special_leds_result}")
            if send_value_to_cpu(value=value_to_send, address=13):
                app_state.led_analysis_state = 'CLEARING'
                logging.info(f"Signal {value_to_send} to addr 13 sent successfully.")
            else:
                logging.error(f"Failed to send special LEDs result ({value_to_send} to addr 13). Retrying.")
                # 状态保持不变，下次循环重试
        else:
            # 如果 last_special_leds_result 是 None (可能因为解析失败)，直接清理
            logging.warning("No special LEDs result found, skipping sending to address 13.")
            app_state.led_analysis_state = 'CLEARING'

    # --- CLEARING State: 清空日志文件 ---
    elif app_state.led_analysis_state == 'CLEARING':
        logging.info(f"Clearing log file: {LOG_FILE}")
        try:
            # 确保 handler 已移除
            if app_state.log_file_handler and app_state.log_file_handler in logger.handlers:
                 logger.removeHandler(app_state.log_file_handler)
                 logging.info("FileHandler removed before clearing.") # 添加 handler 日志

            # 清空文件
            with open(LOG_FILE, 'w', encoding='utf-8') as f:
                pass
            logging.info("Log file cleared successfully.")

            # 重置状态
            app_state.logging_start_time = None
            app_state.last_analysis_result = None
            app_state.led_analysis_state = 'IDLE'
            logging.info("'88' analysis cycle complete. Returning to IDLE state.")

        except OSError as e:
            print(f"Error clearing log file: {e}. Will attempt clear next cycle.")
            logging.error(f"Error clearing log file: {e}. State remains CLEARING.")
            # 状态保持 CLEARING，下次循环重试清空
        except Exception as e:
             print(f"Unexpected error during log clearing: {e}")
             logging.error(f"Unexpected error during log clearing: {e}. Returning to IDLE.")
             # 发生意外错误，最好回到 IDLE 状态避免卡死
             app_state.led_analysis_state = 'IDLE'

    # --- 每帧日志记录 (现在由 handler 控制是否写入) ---
    if app_state.led_max_rois > 0:
        all_current_gray_values = [v[0] for v in app_state.led_last_values if isinstance(v, tuple) and len(v) > 0] # 添加检查
        max_gray = max(all_current_gray_values) if all_current_gray_values else 0.0

        for i in range(app_state.led_max_rois):
            if not app_state.led_rois[i]: continue
            if i >= len(app_state.led_last_status) or i >= len(app_state.led_last_values): continue # 边界检查

            is_on = app_state.led_last_status[i]
            values = app_state.led_last_values[i]
            # 添加更健壮的值检查
            if not isinstance(values, tuple) or len(values) < 3:
                gray_val, green_val, red_val = 0.0, 0.0, 0.0
            else:
                gray_val, green_val, red_val = values[0], values[1], values[2]

            status_text = "ON" if is_on else "OFF"
            brightness_ratio = (gray_val / max_gray * 100) if max_gray > 0 else 0
            is_green_led = (i < app_state.led_num_green)
            led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"
            log_message = (f"LED {led_label}: Status={status_text}, Gray={gray_val:.1f}, "
                           f"Green={green_val:.1f}, Red={red_val:.1f}, Brightness={brightness_ratio:.1f}%")
            logging.info(log_message) # logging.info 会被 FileHandler 捕获（如果 handler 被添加了）

    # --- 处理按键 (阈值调整等，保持不变) ---
    key = cv2.waitKey(1) & 0xFF
    threshold_changed = False
    if key == ord('q'):
        app_state.running = False
    elif key == ord('c'):
        # 如果正在执行分析流程，不允许切换到校准
        if app_state.led_analysis_state != 'IDLE':
            print("Warning: Cannot switch to Calibration mode while analysis process is active.")
            logging.warning("Attempted to switch to Calibration mode during active analysis process.")
        else:
            logging.info("Switching to Calibration mode.")
            app_state.current_mode = MODE_CALIBRATION
            app_state.current_calib_state = CALIB_STATE_START
    elif key == ord('s'):
        logging.info("Saving current thresholds to config...")
        if config_manager.save_config(app_state):
             if app_state.display_frame is not None:
                  cv2.putText(app_state.display_frame, "Thresholds Saved!", (app_state.display_frame.shape[1] // 2 - 100, app_state.display_frame.shape[0] // 2),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
    elif key == ord('l'):
        # 快速保存LED样本和阈值（不保存ROI坐标）
        logging.info("Quick saving LED samples and thresholds only...")
        if config_manager.save_led_samples_and_thresholds_only(app_state):
            if app_state.display_frame is not None:
                cv2.putText(app_state.display_frame, "LED Samples & Thresholds Saved!", (app_state.display_frame.shape[1] // 2 - 150, app_state.display_frame.shape[0] // 2),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            print("✓ LED样本数据和阈值已快速更新，ROI坐标保持不变")
        else:
            print("✗ 快速更新失败")
    elif key == ord('b'):
        # 切换基准点对齐功能
        app_state.alignment_enabled = not app_state.alignment_enabled
        status = "True" if app_state.alignment_enabled else "Disable"
        print(f"基准点对齐功能已{status}")

        # 验证基准点配置
        if app_state.alignment_enabled:
            is_healthy, issues = base_point_manager.validate_alignment_system(app_state)
            if not is_healthy:
                print("⚠ 基准点配置存在问题:")
                for issue in issues:
                    print(f"  - {issue}")
            else:
                print("✓ 基准点配置验证通过")

        # 在屏幕上显示状态变化
        if app_state.display_frame is not None:
            status_text = f"Base Alignment: {status}"
            cv2.putText(app_state.display_frame, status_text,
                       (app_state.display_frame.shape[1] // 2 - 120, app_state.display_frame.shape[0] // 2),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0) if app_state.alignment_enabled else (0, 0, 255), 2)

    # LED 阈值调整
    elif key == ord('g'): app_state.led_gray_threshold_green = max(0, app_state.led_gray_threshold_green - LED_THRESHOLD_STEP); threshold_changed = True
    elif key == ord('G'): app_state.led_gray_threshold_green += LED_THRESHOLD_STEP; threshold_changed = True
    elif key == ord('v'): app_state.led_green_threshold = max(0, app_state.led_green_threshold - LED_THRESHOLD_STEP); threshold_changed = True
    elif key == ord('V'): app_state.led_green_threshold += LED_THRESHOLD_STEP; threshold_changed = True
    elif key == ord('y'): app_state.led_gray_threshold_red = max(0, app_state.led_gray_threshold_red - LED_THRESHOLD_STEP); threshold_changed = True
    elif key == ord('Y'): app_state.led_gray_threshold_red += LED_THRESHOLD_STEP; threshold_changed = True
    elif key == ord('r'): app_state.led_red_threshold = max(0, app_state.led_red_threshold - LED_THRESHOLD_STEP); threshold_changed = True
    elif key == ord('R'): app_state.led_red_threshold += LED_THRESHOLD_STEP; threshold_changed = True
    # 数码管阈值调整
    elif key == ord('+') or key == ord('='): app_state.digit_brightness_threshold += DIGIT_THRESHOLD_STEP; threshold_changed = True
    elif key == ord('-') or key == ord('_'): app_state.digit_brightness_threshold = max(0.0, app_state.digit_brightness_threshold - DIGIT_THRESHOLD_STEP); threshold_changed = True

    if threshold_changed:
         logging.info(f"Threshold Updated: G(G={app_state.led_gray_threshold_green:.1f},Gn={app_state.led_green_threshold:.1f})|R(G={app_state.led_gray_threshold_red:.1f},Rd={app_state.led_red_threshold:.1f})|D={app_state.digit_brightness_threshold:.1f}")

    # --- 设置底部提示信息 (根据状态机状态显示不同信息) ---
    if app_state.led_analysis_state == 'IDLE':
        app_state.prompt_message = "MODE: Detection | 'C' Calibrate | 'B' Toggle Base Align | 'S' Save All | 'L' Save LED Only | 'Q' Quit"
    elif app_state.led_analysis_state == 'LOGGING':
        # Status message already set in the LOGGING state logic
        app_state.prompt_message = "Logging active... Press 'Q' to Quit (will interrupt process)"
    elif app_state.led_analysis_state in ['WAITING_TO_SIGNAL_12', 'ANALYZING', 'SENDING_RESULT_10', 'SENDING_RESULT_13', 'CLEARING']:
        app_state.status_message = f"Processing '88' analysis (State: {app_state.led_analysis_state})... Please wait."
        app_state.prompt_message = "Press 'Q' to Quit (will interrupt process)"
    else: # Unknown state?
        app_state.prompt_message = "MODE: Detection | 'Q' Quit"


def process_ui_and_logic(app_state: AppState):
    """主处理函数，根据当前模式调用相应逻辑并更新UI"""

    # --- 更新 FPS ---
    curr_time = time.time()
    time_diff = curr_time - app_state.prev_time
    if time_diff > 0:
        app_state.fps = 1.0 / time_diff
    app_state.prev_time = curr_time

    # --- 清空上一帧的消息 (现在在 _run_detection_mode 开始时处理) ---
    # app_state.prompt_message = ""
    # app_state.status_message = ""

    # --- 根据模式执行核心逻辑和事件处理 ---
    if app_state.current_mode == MODE_CAMERA_SETTINGS:
        app_state.prompt_message = ""
        app_state.status_message = ""
        _run_camera_settings_mode(app_state)
    elif app_state.current_mode == MODE_CALIBRATION:
        app_state.prompt_message = ""
        app_state.status_message = ""
        _run_calibration_mode(app_state)
    elif app_state.current_mode == MODE_DETECTION:
        app_state.prompt_message = "" # 清空，由 _run_detection_mode 设置
        app_state.status_message = "" # 清空，由 _run_detection_mode 设置
        _run_detection_mode(app_state)
    else:
        print(f"错误: 未知模式 {app_state.current_mode}")
        app_state.running = False

    # --- 绘制界面元素 (ROIs, HUD) ---
    if app_state.display_frame is not None and isinstance(app_state.display_frame, np.ndarray):
        # --- 重新添加 draw_rois 调用 --- #
        # 它需要在模式逻辑执行后，HUD 绘制前调用
        # 以便在 display_frame 上绘制模式相关的 ROI 信息
        draw_rois(app_state)
        # ------------------------------ #

        # 绘制 HUD (状态信息、提示等)
        draw_hud(app_state)

        # 显示最终结果
        cv2.imshow(MAIN_WINDOW, app_state.display_frame)
    else:
        # 显示错误信息帧
        error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.putText(error_frame, "Error: display_frame is invalid", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.imshow(MAIN_WINDOW, error_frame)
