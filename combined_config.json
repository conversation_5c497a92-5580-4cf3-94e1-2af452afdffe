{"version": "1.1", "timestamp": "2025-08-11 17:29:40", "camera_settings": {"resolution_index": 2, "exposure": -5.0, "brightness": 0.0}, "led_settings": {"num_green": 33, "num_red": 2, "rois": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "off_samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "on_samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "gray_threshold_green": 160.0, "green_threshold": 180.0, "gray_threshold_red": 160.0, "red_threshold": 100.0}, "digit_settings": {"digit_rois": [null, null], "segment_rois": [[null, null, null, null, null, null, null], [null, null, null, null, null, null, null]], "brightness_threshold": 50.0}, "base_alignment": {"enabled": true, "base_points": [null, null], "original_base_points": [null, null], "template_size": 30, "match_threshold": 0.75, "base_templates_b64": [null, null]}, "original_rois": {"led_rois": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "digit_rois": [null, null], "digit_segment_rois": [[null, null, null, null, null, null, null], [null, null, null, null, null, null, null]]}, "analysis_settings": {"logging_duration": 50.0}}